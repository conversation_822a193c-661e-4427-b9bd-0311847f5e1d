# Guide de Test des Améliorations des Tickets d'Achat

## Objectif
Vérifier que les améliorations apportées aux tickets d'achat dans les modes POS et formulaire fonctionnent correctement et affichent clairement les informations de prix et remises.

## Améliorations Implémentées

### 1. Affichage des Prix dans les Tickets d'Achat
- **Prix de référence** : Toujours affiché (prix original du produit/ingrédient)
- **Prix modifié** : Affiché si l'utilisateur a modifié le prix manuellement
- **Prix après remise** : Affiché si une remise individuelle a été appliquée

### 2. Affichage des Totaux et Remises
- **Remise sur prix** : Montant total des remises appliquées aux prix individuels
- **Remise globale** : Remise appliquée sur le total (avec note explicative)
- **Notes explicatives** :
  - "(sur total)" : quand la remise est appliquée au total
  - "(appliquée sur prix)" : quand la remise est appliquée aux prix individuels

### 3. Modals de Paiement
- Affichage des mêmes informations détaillées que dans les tickets principaux
- Cohérence entre les deux modes (POS et formulaire)

## Tests à Effectuer

### Test 1 : Mode POS - Prix Normal
1. Aller sur http://127.0.0.1:5000/inventory/stock-replenishment/pos-mode
2. Choisir un fournisseur
3. Ajouter des articles au panier
4. Vérifier que les prix s'affichent normalement dans le ticket

### Test 2 : Mode POS - Modification de Prix
1. Ajouter un article au panier
2. Cliquer sur le bouton "Modifier le prix" d'un article
3. Changer le prix et confirmer
4. Vérifier l'affichage :
   - Prix référentiel affiché
   - Prix modifié affiché avec indication

### Test 3 : Mode POS - Remise sur Prix Individuels
1. Ajouter plusieurs articles au panier
2. Cliquer sur "Appliquer une remise"
3. Cocher "Appliquer la remise aux prix d'achat individuels"
4. Entrer une remise (ex: 10%)
5. Confirmer et vérifier :
   - Section "Remise sur prix" affichée avec le montant
   - Prix des articles mis à jour avec détails
   - Section "Remise (appliquée sur prix)" affiche 0.00 €

### Test 4 : Mode POS - Remise Globale
1. Ajouter plusieurs articles au panier
2. Cliquer sur "Appliquer une remise"
3. NE PAS cocher "Appliquer la remise aux prix d'achat individuels"
4. Entrer une remise (ex: 50€)
5. Confirmer et vérifier :
   - Section "Remise (sur total)" affichée avec le montant
   - Prix des articles inchangés

### Test 5 : Mode POS - Modal de Paiement
1. Après avoir appliqué une remise (test 3 ou 4)
2. Cliquer sur "Payer la marchandise"
3. Vérifier que la modal affiche les mêmes informations que le ticket

### Test 6 : Mode Formulaire - Mêmes Tests
1. Répéter les tests 1-5 sur http://127.0.0.1:5000/inventory/stock-replenishment/form-mode
2. Vérifier la cohérence avec le mode POS

### Test 7 : Détails de Commande
1. Finaliser une commande avec remise (mode POS ou formulaire)
2. Aller sur la page des détails de la commande
3. Vérifier que toutes les informations sont correctement affichées :
   - Prix référentiels et modifiés
   - Remises appliquées
   - Totaux clairs

## Résultats Attendus

### Affichage des Articles
- Chaque article doit clairement montrer :
  - Le prix final utilisé
  - Le prix de référence (si différent)
  - Le prix modifié (si applicable)
  - Le prix après remise (si remise individuelle)

### Affichage des Totaux
- Distinction claire entre :
  - Remises appliquées aux prix individuels
  - Remises appliquées au total
- Notes explicatives entre parenthèses
- Cohérence entre ticket principal et modal de paiement

### Cohérence
- Mêmes informations dans les deux modes
- Mêmes informations dans les détails de commande
- Calculs corrects dans tous les cas

## Problèmes Potentiels à Vérifier
1. Remises individuelles et globales ne doivent pas se cumuler
2. Les prix de référence doivent être préservés
3. Les notes explicatives doivent être claires
4. Les calculs doivent être exacts
5. L'interface doit rester lisible même avec beaucoup d'informations
