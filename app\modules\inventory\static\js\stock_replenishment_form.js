// JavaScript pour le mode formulaire d'approvisionnement
const StockReplenishmentForm = {
    currentOrder: [],
    selectedSupplier: null,
    orderTotal: 0,
    discount: {
        amount: 0,
        type: 'amount',
        applyToItems: false
    },
    editingItem: null,

    init: function() {
        this.initEventListeners();
        this.updateOrderDisplay();
        this.initializeFormState();
    },

    initEventListeners: function() {
        // Changement de catégorie de fournisseur
        document.getElementById('supplierCategorySelect').addEventListener('change', (e) => {
            this.loadSuppliersByCategory(e.target.value);
        });

        // Changement de fournisseur
        document.getElementById('supplierSelect').addEventListener('change', (e) => {
            this.selectSupplier(e.target.value);
        });

        // Changement de type d'article
        document.getElementById('itemTypeSelect').addEventListener('change', (e) => {
            this.toggleItemSelect(e.target.value);
        });

        // Changement d'article sélectionné
        document.getElementById('productSelect').addEventListener('change', (e) => {
            this.loadItemInfo(e.target.value, 'product');
        });

        document.getElementById('ingredientSelect').addEventListener('change', (e) => {
            this.loadItemInfo(e.target.value, 'ingredient');
        });

        // Calcul automatique du total
        document.getElementById('quantity').addEventListener('input', () => {
            this.calculateTotal();
        });

        document.getElementById('unit_price').addEventListener('input', () => {
            this.calculateTotal();
        });

        // Soumission du formulaire
        document.getElementById('stockReplenishmentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addItemToOrder();
        });
    },

    loadSuppliersByCategory: function(categoryId) {
        const supplierSelect = document.getElementById('supplierSelect');
        
        if (categoryId == 0) {
            // Recharger tous les fournisseurs
            this.loadAllSuppliers();
        } else {
            // Charger les fournisseurs de la catégorie
            fetch(`/inventory/stock-replenishment/api/suppliers/${categoryId}`)
                .then(response => response.json())
                .then(suppliers => {
                    supplierSelect.innerHTML = '<option value="0">Aucun fournisseur</option>' +
                        suppliers.map(s => 
                            `<option value="${s.id}">${s.name}</option>`
                        ).join('');
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des fournisseurs:', error);
                });
        }
    },

    loadAllSuppliers: function() {
        // Cette fonction devrait charger tous les fournisseurs
        // Pour l'instant, on garde les options existantes
    },

    selectSupplier: function(supplierId) {
        this.selectedSupplier = supplierId == 0 ? null : supplierId;

        const supplierInfoForm = document.getElementById('supplierInfoForm');
        if (this.selectedSupplier) {
            const supplierSelect = document.getElementById('supplierSelect');
            const selectedOption = supplierSelect.options[supplierSelect.selectedIndex];
            if (supplierInfoForm) {
                supplierInfoForm.innerHTML = `<i class="fas fa-truck me-1"></i>Fournisseur: ${selectedOption.text}`;
            }
        } else {
            if (supplierInfoForm) {
                supplierInfoForm.innerHTML = '<i class="fas fa-truck me-1"></i>Fournisseur: Autres';
            }
        }
    },

    initializeFormState: function() {
        // Initialiser l'état du formulaire au chargement
        const itemTypeSelect = document.getElementById('itemTypeSelect');
        const productDiv = document.getElementById('productSelectDiv');
        const ingredientDiv = document.getElementById('ingredientSelectDiv');

        // Cacher les deux divs au départ
        productDiv.style.display = 'none';
        ingredientDiv.style.display = 'none';

        // Si un type est déjà sélectionné, l'afficher
        if (itemTypeSelect.value) {
            this.toggleItemSelect(itemTypeSelect.value);
        }
    },

    toggleItemSelect: function(itemType) {
        const productDiv = document.getElementById('productSelectDiv');
        const ingredientDiv = document.getElementById('ingredientSelectDiv');
        const label = document.getElementById('itemSelectLabel');

        if (itemType === 'product') {
            productDiv.style.display = 'block';
            ingredientDiv.style.display = 'none';
            label.textContent = 'Produit sans recette';
        } else if (itemType === 'ingredient') {
            productDiv.style.display = 'none';
            ingredientDiv.style.display = 'block';
            label.textContent = 'Ingrédient';
        } else {
            // Si aucun type sélectionné, cacher les deux
            productDiv.style.display = 'none';
            ingredientDiv.style.display = 'none';
            label.textContent = 'Article';
        }

        // Réinitialiser les informations de l'article
        this.hideItemInfo();

        // Réinitialiser les prix
        document.getElementById('unit_price').value = '';
        this.calculateTotal();
    },

    loadItemInfo: function(itemId, itemType) {
        if (!itemId || itemId == 0) {
            this.hideItemInfo();
            return;
        }

        // Simuler le chargement des informations de l'article
        // Dans une vraie implémentation, ceci ferait un appel API
        const itemInfoCard = document.getElementById('itemInfoCard');
        const itemInfoContent = document.getElementById('itemInfoContent');

        itemInfoCard.style.display = 'block';
        itemInfoContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Type:</strong> ${itemType === 'product' ? 'Produit' : 'Ingrédient'}<br>
                    <strong>Stock actuel:</strong> <span class="text-info">Chargement...</span><br>
                    <strong>Stock minimum:</strong> <span class="text-warning">Chargement...</span>
                </div>
                <div class="col-md-6">
                    <strong>Unité:</strong> <span>Chargement...</span><br>
                    <strong>Dernier prix:</strong> <span class="text-success">Chargement...</span><br>
                    <strong>Fournisseur habituel:</strong> <span>Chargement...</span>
                </div>
            </div>
        `;

        // Charger les vraies informations via API
        this.fetchItemDetails(itemId, itemType);
    },

    fetchItemDetails: function(itemId, itemType) {
        // Appel API pour récupérer les détails de l'article
        fetch(`/inventory/api/item-details/${itemType}/${itemId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    this.updateItemInfo(data);
                    // Pré-remplir le prix si disponible
                    const unitPriceField = document.getElementById('unit_price');
                    if (data.last_price && data.last_price > 0) {
                        unitPriceField.value = data.last_price.toFixed(2);
                    } else if (data.price && data.price > 0) {
                        // Utiliser le prix de vente comme référence si pas de dernier prix d'achat
                        unitPriceField.value = data.price.toFixed(2);
                    } else {
                        unitPriceField.value = '';
                    }
                    this.calculateTotal();
                } else {
                    console.error('Erreur dans la réponse API:', data.error || 'Erreur inconnue');
                    this.showItemInfoError();
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des détails:', error);
                this.showItemInfoError();
            });
    },

    updateItemInfo: function(data) {
        const itemInfoContent = document.getElementById('itemInfoContent');
        itemInfoContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Type:</strong> ${data.type === 'product' ? 'Produit' : 'Ingrédient'}<br>
                    <strong>Stock actuel:</strong> <span class="text-info">${data.stock_quantity} ${data.unit}</span><br>
                    <strong>Stock minimum:</strong> <span class="text-warning">${data.minimum_stock} ${data.unit}</span>
                </div>
                <div class="col-md-6">
                    <strong>Unité:</strong> ${data.unit}<br>
                    <strong>Dernier prix:</strong> <span class="text-success">${data.last_price ? data.last_price.toFixed(2) + ' €' : 'N/A'}</span><br>
                    <strong>Fournisseur habituel:</strong> ${data.usual_supplier || 'Aucun'}
                </div>
            </div>
        `;

        // Stocker l'unité pour l'utiliser lors de l'ajout à la commande
        this.currentItemUnit = data.unit;
    },

    hideItemInfo: function() {
        const itemInfoCard = document.getElementById('itemInfoCard');
        if (itemInfoCard) {
            itemInfoCard.style.display = 'none';
        }
    },

    showItemInfoError: function() {
        const itemInfoCard = document.getElementById('itemInfoCard');
        const itemInfoContent = document.getElementById('itemInfoContent');

        if (itemInfoCard && itemInfoContent) {
            itemInfoCard.style.display = 'block';
            itemInfoContent.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Impossible de charger les informations de cet article.
                </div>
            `;
        }
    },

    calculateTotal: function() {
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;
        const total = quantity * unitPrice;
        
        document.getElementById('totalPrice').value = total.toFixed(2) + ' €';
    },

    addItemToOrder: function() {
        const form = document.getElementById('stockReplenishmentForm');
        const formData = new FormData(form);
        
        const itemType = formData.get('item_type');
        const itemId = itemType === 'product' ? 
            formData.get('product_id') : formData.get('ingredient_id');
        
        if (!itemId || itemId == 0) {
            alert('Veuillez sélectionner un article');
            return;
        }

        const quantity = parseFloat(formData.get('quantity'));
        const unitPrice = parseFloat(formData.get('unit_price'));

        if (!quantity || quantity <= 0) {
            alert('Veuillez saisir une quantité valide');
            return;
        }

        if (!unitPrice || unitPrice < 0) {
            alert('Veuillez saisir un prix valide');
            return;
        }

        // Récupérer le nom de l'article
        const itemSelect = itemType === 'product' ? 
            document.getElementById('productSelect') : 
            document.getElementById('ingredientSelect');
        const itemName = itemSelect.options[itemSelect.selectedIndex].text;

        // Vérifier si l'article est déjà dans la commande
        const existingIndex = this.currentOrder.findIndex(item => 
            item.type === itemType && item.id == itemId
        );

        if (existingIndex >= 0) {
            // Mettre à jour l'article existant
            this.currentOrder[existingIndex].quantity += quantity;
            this.currentOrder[existingIndex].total_price =
                this.currentOrder[existingIndex].quantity * this.currentOrder[existingIndex].unit_price;
        } else {
            // Ajouter nouvel article
            this.currentOrder.push({
                id: itemId,
                type: itemType,
                name: itemName,
                quantity: quantity,
                unit: this.currentItemUnit || '',
                unit_price: unitPrice,
                total_price: quantity * unitPrice,
                notes: formData.get('notes') || '',
                expected_delivery_date: formData.get('expected_delivery_date') || null
            });
        }

        this.updateOrderDisplay();
        this.resetForm();
    },

    updateOrderDisplay: function() {
        const ticketItems = document.getElementById('ticketItemsForm');
        const subtotalAmount = document.getElementById('subtotalAmountForm');
        const totalAmount = document.getElementById('totalAmountForm');
        const discountRow = document.getElementById('discountRowForm');
        const discountAmount = document.getElementById('discountAmountForm');
        const discountButton = document.getElementById('discountButtonForm');
        const payButton = document.getElementById('payButtonForm');

        if (this.currentOrder.length === 0) {
            ticketItems.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>Aucun article sélectionné</p>
                </div>
            `;
            if (subtotalAmount) subtotalAmount.textContent = '0.00 €';
            if (totalAmount) totalAmount.textContent = '0.00 €';
            if (discountButton) discountButton.disabled = true;
            if (payButton) payButton.disabled = true;
            return;
        }

        const itemsHtml = this.currentOrder.map((item, index) => {
            // Construire l'affichage des prix de manière claire (même logique que le mode POS)
            let priceDisplay = `${item.quantity} ${item.unit || ''} × ${item.unit_price.toFixed(2)} €`;
            let priceInfo = '';

            // Prix de référence (toujours affiché)
            const referencePrice = item.originalPrice || item.unit_price;

            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                // Cas: remise appliquée aux prix individuels
                priceInfo += `<br><small class="text-muted">Prix référentiel: ${referencePrice.toFixed(2)} €</small>`;
                if (item.priceBeforeDiscount !== referencePrice) {
                    priceInfo += `<br><small class="text-info">Prix modifié: ${item.priceBeforeDiscount.toFixed(2)} €</small>`;
                }
                priceInfo += `<br><small class="text-success">Prix après remise: ${item.unit_price.toFixed(2)} €</small>`;
            } else if (item.originalPrice && item.originalPrice !== item.unit_price) {
                // Cas: prix modifié manuellement (sans remise individuelle)
                priceInfo += `<br><small class="text-muted">Prix référentiel: ${referencePrice.toFixed(2)} €</small>`;
                priceInfo += `<br><small class="text-warning">Prix modifié: ${item.unit_price.toFixed(2)} €</small>`;
            } else if (referencePrice !== item.unit_price) {
                // Cas: prix différent du référentiel (pour d'autres raisons)
                priceInfo += `<br><small class="text-muted">Prix référentiel: ${referencePrice.toFixed(2)} €</small>`;
            }

            return `
                <div class="ticket-item">
                    <div class="d-flex justify-content-between align-items-start mb-1">
                        <div class="item-info flex-grow-1">
                            <div class="item-name">${item.name}</div>
                            <div class="item-details">
                                ${priceDisplay}${priceInfo}
                                ${item.notes ? `<br><small class="text-muted">Note: ${item.notes}</small>` : ''}
                            </div>
                        </div>
                        <div class="item-actions">
                            <span class="item-total">${item.total_price.toFixed(2)} €</span>
                            <button class="btn btn-sm btn-outline-primary ms-1"
                                    onclick="StockReplenishmentForm.editItemPriceForm(${index})"
                                    title="Modifier le prix">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger ms-1"
                                    onclick="StockReplenishmentForm.removeItem(${index})"
                                    title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        ticketItems.innerHTML = itemsHtml;

        // Calcul des totaux avec distinction claire entre remises individuelles et globales
        let subtotalBeforeDiscounts = 0;
        let individualDiscountTotal = 0;

        this.currentOrder.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                // Article avec remise individuelle
                subtotalBeforeDiscounts += item.quantity * item.priceBeforeDiscount;
                individualDiscountTotal += item.quantity * (item.priceBeforeDiscount - item.unit_price);
            } else {
                // Article sans remise individuelle
                subtotalBeforeDiscounts += item.total_price;
            }
        });

        // Le sous-total affiché est le total actuel (après remises individuelles)
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);
        let globalDiscountAmount = 0;

        // Vérifier si des remises individuelles ont été appliquées
        const hasIndividualDiscounts = this.currentOrder.some(item => item.hasIndividualDiscount);

        // Les remises globales ne s'appliquent que si pas de remises individuelles
        if (this.discount.amount > 0 && !hasIndividualDiscounts) {
            if (this.discount.type === 'percentage') {
                globalDiscountAmount = subtotal * (this.discount.amount / 100);
            } else {
                globalDiscountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - globalDiscountAmount;
        this.orderTotal = total;

        if (subtotalAmount) subtotalAmount.textContent = subtotal.toFixed(2) + ' €';
        if (totalAmount) totalAmount.textContent = total.toFixed(2) + ' €';

        // Affichage des remises individuelles
        const individualDiscountRow = document.getElementById('individualDiscountRowForm');
        const individualDiscountAmountEl = document.getElementById('individualDiscountAmountForm');
        if (individualDiscountTotal > 0 && individualDiscountRow && individualDiscountAmountEl) {
            individualDiscountRow.style.display = 'flex';
            individualDiscountAmountEl.textContent = '-' + individualDiscountTotal.toFixed(2) + ' €';
        } else if (individualDiscountRow) {
            individualDiscountRow.style.display = 'none';
        }

        // Affichage des remises globales
        const globalDiscountRow = document.getElementById('globalDiscountRowForm');
        const globalDiscountAmountEl = document.getElementById('globalDiscountAmountForm');
        const globalDiscountNote = document.getElementById('globalDiscountNoteForm');

        if (globalDiscountAmount > 0 && globalDiscountRow && globalDiscountAmountEl) {
            globalDiscountRow.style.display = 'flex';
            globalDiscountAmountEl.textContent = '-' + globalDiscountAmount.toFixed(2) + ' €';
            if (globalDiscountNote) {
                globalDiscountNote.textContent = '(sur total)';
            }
        } else if (globalDiscountRow) {
            globalDiscountRow.style.display = 'none';
        }

        // Si remises individuelles appliquées, afficher 0 pour remise globale avec note explicative
        if (hasIndividualDiscounts && this.discount.amount > 0 && globalDiscountRow && globalDiscountAmountEl) {
            globalDiscountRow.style.display = 'flex';
            globalDiscountAmountEl.textContent = '0.00 €';
            if (globalDiscountNote) {
                globalDiscountNote.textContent = '(appliquée sur prix)';
            }
        }

        if (discountButton) discountButton.disabled = false;
        if (payButton) payButton.disabled = false;
    },

    removeItem: function(index) {
        this.currentOrder.splice(index, 1);
        this.updateOrderDisplay();
    },

    clearOrder: function() {
        this.currentOrder = [];
        this.discount = { amount: 0, type: 'amount', applyToItems: false };
        this.updateOrderDisplay();
    },

    clearDiscount: function() {
        this.resetIndividualDiscountsForm();
        this.discount = { amount: 0, type: 'amount', applyToItems: false };
        this.updateOrderDisplay();
    },

    resetForm: function() {
        const form = document.getElementById('stockReplenishmentForm');
        form.reset();

        // Réinitialiser les champs spécifiques
        document.getElementById('totalPrice').value = '';
        document.getElementById('unit_price').value = '';

        // Cacher les informations de l'article
        this.hideItemInfo();

        // Réinitialiser l'affichage des sélecteurs d'articles
        // Ne pas forcer un type par défaut, laisser l'utilisateur choisir
        this.toggleItemSelect(''); // Cacher tous les sélecteurs

        // Remettre le type d'article à vide
        const itemTypeSelect = document.getElementById('itemTypeSelect');
        itemTypeSelect.value = '';
    },

    finalizeOrder: function() {
        if (this.currentOrder.length === 0) {
            alert('Aucun article dans la commande');
            return;
        }

        // Préparer le résumé de la commande
        const summaryContent = document.getElementById('orderSummaryContent');
        summaryContent.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Quantité</th>
                            <th>Prix unitaire</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.currentOrder.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${item.unit_price.toFixed(2)} €</td>
                                <td>${item.total_price.toFixed(2)} €</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td colspan="3">Total</td>
                            <td>${this.orderTotal.toFixed(2)} €</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;

        // Afficher la modale
        const modal = new bootstrap.Modal(document.getElementById('finalizeModal'));
        modal.show();
    },

    submitOrder: function() {
        const processingType = document.querySelector('input[name="processingType"]:checked').value;
        const generalNotes = document.getElementById('generalNotes').value;

        // Calculer les totaux avec la même logique que la modal de paiement
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);
        let globalDiscountAmount = 0;

        // Vérifier si des remises individuelles ont été appliquées
        const hasIndividualDiscounts = this.currentOrder.some(item => item.hasIndividualDiscount);

        // Les remises globales ne s'appliquent que si pas de remises individuelles
        if (this.discount.amount > 0 && !hasIndividualDiscounts) {
            if (this.discount.type === 'percentage') {
                globalDiscountAmount = subtotal * (this.discount.amount / 100);
            } else {
                globalDiscountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - globalDiscountAmount;

        const orderData = {
            supplier_id: this.selectedSupplier,
            cart_items: this.currentOrder.map(item => ({
                type: item.type,
                product_id: item.type === 'product' ? item.id : null,
                ingredient_id: item.type === 'ingredient' ? item.id : null,
                quantity: item.quantity,
                unit_price: item.unit_price,
                // Si une remise individuelle a été appliquée, utiliser le prix avant remise
                // Sinon, utiliser le prix original ou le prix actuel
                original_unit_price: item.hasIndividualDiscount ?
                    (item.priceBeforeDiscount || item.originalPrice || item.unit_price) :
                    (item.originalPrice || item.unit_price),
                total_price: item.total_price,
                notes: item.notes || '',
                price_change_reason: item.priceChangeReason || null,
                expected_delivery_date: item.expected_delivery_date
            })),
            subtotal: subtotal,
            discount: {
                amount: this.discount.amount,
                type: this.discount.type,
                apply_to_items: this.discount.applyToItems
            },
            total_amount: total,
            processing_type: processingType,
            notes: generalNotes
        };

        // Envoyer la commande
        fetch('/inventory/stock-replenishment/process-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande créée avec succès!');
                this.clearOrder();
                bootstrap.Modal.getInstance(document.getElementById('finalizeModal')).hide();
                
                // Rediriger vers les détails de la commande
                if (data.purchase_order_id) {
                    window.location.href = `/inventory/stock-replenishment/orders/${data.purchase_order_id}`;
                }
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    },

    // Nouvelles méthodes pour la gestion des prix et remises
    editItemPriceForm: function(index) {
        const item = this.currentOrder[index];
        if (!item) return;

        this.editingItem = { index, item };

        // Remplir la modal
        document.getElementById('editItemNameForm').textContent = item.name;
        document.getElementById('editReferencePriceForm').textContent = (item.originalPrice || item.unit_price).toFixed(2) + ' €';
        document.getElementById('editCurrentPriceForm').textContent = item.unit_price.toFixed(2) + ' €';
        document.getElementById('newPriceForm').value = item.unit_price.toFixed(2);

        // Ouvrir la modal
        const modal = new bootstrap.Modal(document.getElementById('priceEditModalForm'));
        modal.show();
    },

    confirmPriceChangeForm: function() {
        if (!this.editingItem) return;

        const newPrice = parseFloat(document.getElementById('newPriceForm').value);
        const reason = document.getElementById('priceChangeReasonForm').value;

        if (isNaN(newPrice) || newPrice < 0) {
            alert('Veuillez entrer un prix valide');
            return;
        }

        const { index, item } = this.editingItem;

        // Sauvegarder le prix de référence initial si ce n'est pas déjà fait
        if (!item.referencePrice) {
            item.referencePrice = item.originalPrice || item.unit_price;
        }

        // Sauvegarder le prix original (avant modification manuelle) si ce n'est pas déjà fait
        if (!item.originalPrice) {
            item.originalPrice = item.unit_price;
        }

        // Mettre à jour le prix
        this.currentOrder[index].unit_price = newPrice;
        this.currentOrder[index].total_price = this.currentOrder[index].quantity * newPrice;
        this.currentOrder[index].priceChangeReason = reason;
        this.currentOrder[index].hasManualPriceChange = true;

        // Mettre à jour l'affichage
        this.updateOrderDisplay();

        // Fermer la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('priceEditModalForm'));
        modal.hide();

        this.editingItem = null;
    },

    openDiscountModal: function() {
        // Vérifier qu'il y a des articles dans la commande
        if (this.currentOrder.length === 0) {
            alert('Veuillez ajouter des articles à la commande avant d\'appliquer une remise');
            return;
        }

        // Calculer le sous-total en utilisant les prix actuels (possiblement modifiés)
        // Si des remises individuelles sont appliquées, utiliser les prix avant remise
        let subtotal = 0;
        this.currentOrder.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                subtotal += item.quantity * item.priceBeforeDiscount;
            } else {
                subtotal += item.total_price;
            }
        });

        const modal = new bootstrap.Modal(document.getElementById('discountModalForm'));
        modal.show();

        // Attendre que la modal soit complètement affichée
        setTimeout(() => {
            const currentSubtotalElement = document.getElementById('currentSubtotalForm');
            if (!currentSubtotalElement) {
                console.error('Element currentSubtotalForm not found');
                return;
            }
            currentSubtotalElement.textContent = subtotal.toFixed(2) + ' €';

            // Réinitialiser les valeurs
            const discountValueElement = document.getElementById('discountValueForm');
            const calculatedDiscountElement = document.getElementById('calculatedDiscountForm');
            const newTotalElement = document.getElementById('newTotalForm');

            if (discountValueElement) discountValueElement.value = '';
            if (calculatedDiscountElement) calculatedDiscountElement.textContent = '0.00 €';
            if (newTotalElement) newTotalElement.textContent = subtotal.toFixed(2) + ' €';
        }, 300);
    },

    openPaymentModal: function() {
        if (this.currentOrder.length === 0) {
            alert('Veuillez ajouter des articles à la commande avant de procéder au paiement');
            return;
        }

        // Calculer les totaux avec la même logique que le ticket principal
        let subtotalBeforeDiscounts = 0;
        let individualDiscountTotal = 0;

        this.currentOrder.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                // Article avec remise individuelle
                subtotalBeforeDiscounts += item.quantity * item.priceBeforeDiscount;
                individualDiscountTotal += item.quantity * (item.priceBeforeDiscount - item.unit_price);
            } else {
                // Article sans remise individuelle
                subtotalBeforeDiscounts += item.total_price;
            }
        });

        // Le sous-total affiché est le total actuel (après remises individuelles)
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);
        let globalDiscountAmount = 0;

        // Vérifier si des remises individuelles ont été appliquées
        const hasIndividualDiscounts = this.currentOrder.some(item => item.hasIndividualDiscount);

        // Les remises globales ne s'appliquent que si pas de remises individuelles
        if (this.discount.amount > 0 && !hasIndividualDiscounts) {
            if (this.discount.type === 'percentage') {
                globalDiscountAmount = subtotal * (this.discount.amount / 100);
            } else {
                globalDiscountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - globalDiscountAmount;

        // Remplir le résumé des articles avec les mêmes détails que le ticket principal
        const paymentOrderItemsForm = document.getElementById('paymentOrderItemsForm');
        if (paymentOrderItemsForm) {
            const itemsHtml = this.currentOrder.map(item => {
                let priceDisplay = `${item.quantity} ${item.unit || ''} × ${item.unit_price.toFixed(2)} €`;
                let priceInfo = '';

                const referencePrice = item.originalPrice || item.unit_price;

                if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                    priceInfo = `<br><small class="text-muted">Réf: ${referencePrice.toFixed(2)} €`;
                    if (item.priceBeforeDiscount !== referencePrice) {
                        priceInfo += ` | Mod: ${item.priceBeforeDiscount.toFixed(2)} €`;
                    }
                    priceInfo += ` | Final: ${item.unit_price.toFixed(2)} €</small>`;
                } else if (item.originalPrice && item.originalPrice !== item.unit_price) {
                    priceInfo = `<br><small class="text-muted">Réf: ${referencePrice.toFixed(2)} € | Mod: ${item.unit_price.toFixed(2)} €</small>`;
                } else if (referencePrice !== item.unit_price) {
                    priceInfo = `<br><small class="text-muted">Réf: ${referencePrice.toFixed(2)} €</small>`;
                }

                return `
                    <div class="d-flex justify-content-between align-items-center py-1">
                        <div class="flex-grow-1">
                            <span class="fw-medium">${item.name}</span>
                            <br><small class="text-muted">${priceDisplay}${priceInfo}</small>
                        </div>
                        <span class="fw-medium">${item.total_price.toFixed(2)} €</span>
                    </div>
                `;
            }).join('');
            paymentOrderItemsForm.innerHTML = itemsHtml;
        }

        // Mettre à jour les totaux avec la même logique que le ticket principal
        const paymentSubtotalForm = document.getElementById('paymentSubtotalForm');
        const paymentTotalForm = document.getElementById('paymentTotalForm');

        if (paymentSubtotalForm) paymentSubtotalForm.textContent = subtotal.toFixed(2) + ' €';
        if (paymentTotalForm) paymentTotalForm.textContent = total.toFixed(2) + ' €';

        // Affichage des remises individuelles
        const paymentIndividualDiscountRowForm = document.getElementById('paymentIndividualDiscountRowForm');
        const paymentIndividualDiscountAmountForm = document.getElementById('paymentIndividualDiscountAmountForm');
        if (individualDiscountTotal > 0 && paymentIndividualDiscountRowForm && paymentIndividualDiscountAmountForm) {
            paymentIndividualDiscountRowForm.style.display = 'flex';
            paymentIndividualDiscountAmountForm.textContent = '-' + individualDiscountTotal.toFixed(2) + ' €';
        } else if (paymentIndividualDiscountRowForm) {
            paymentIndividualDiscountRowForm.style.display = 'none';
        }

        // Affichage des remises globales
        const paymentGlobalDiscountRowForm = document.getElementById('paymentGlobalDiscountRowForm');
        const paymentGlobalDiscountAmountForm = document.getElementById('paymentGlobalDiscountAmountForm');
        const paymentGlobalDiscountNoteForm = document.getElementById('paymentGlobalDiscountNoteForm');

        if (globalDiscountAmount > 0 && paymentGlobalDiscountRowForm && paymentGlobalDiscountAmountForm) {
            paymentGlobalDiscountRowForm.style.display = 'flex';
            paymentGlobalDiscountAmountForm.textContent = '-' + globalDiscountAmount.toFixed(2) + ' €';
            if (paymentGlobalDiscountNoteForm) {
                paymentGlobalDiscountNoteForm.textContent = '(sur total)';
            }
        } else if (paymentGlobalDiscountRowForm) {
            paymentGlobalDiscountRowForm.style.display = 'none';
        }

        // Si remises individuelles appliquées, afficher 0 pour remise globale avec note explicative
        if (hasIndividualDiscounts && this.discount.amount > 0 && paymentGlobalDiscountRowForm && paymentGlobalDiscountAmountForm) {
            paymentGlobalDiscountRowForm.style.display = 'flex';
            paymentGlobalDiscountAmountForm.textContent = '0.00 €';
            if (paymentGlobalDiscountNoteForm) {
                paymentGlobalDiscountNoteForm.textContent = '(appliquée sur prix)';
            }
        }

        const modal = new bootstrap.Modal(document.getElementById('paymentModalForm'));
        modal.show();
    },

    selectPaymentMethod: function(method) {
        this.selectedPaymentMethod = method;

        // Mettre à jour l'affichage des boutons
        document.querySelectorAll('.payment-method-btn-form').forEach(btn => {
            btn.classList.remove('active', 'btn-primary', 'btn-info', 'btn-success', 'btn-warning', 'btn-secondary');
            btn.classList.add('btn-outline-primary', 'btn-outline-info', 'btn-outline-success', 'btn-outline-warning', 'btn-outline-secondary');
        });

        const selectedBtn = document.querySelector(`[data-method="${method}"]`);
        if (selectedBtn) {
            selectedBtn.classList.remove('btn-outline-primary', 'btn-outline-info', 'btn-outline-success', 'btn-outline-warning', 'btn-outline-secondary');
            selectedBtn.classList.add('active');

            // Ajouter la classe de couleur appropriée
            if (method === 'cash_caisse') selectedBtn.classList.add('btn-primary');
            else if (method === 'cheque_compte_banque') selectedBtn.classList.add('btn-info');
            else if (method === 'virement_depuis_compte_banque') selectedBtn.classList.add('btn-success');
            else if (method === 'sortie_cash_banque') selectedBtn.classList.add('btn-warning');
            else selectedBtn.classList.add('btn-secondary');
        }

        // Activer le bouton de confirmation
        const confirmBtn = document.getElementById('confirmPaymentBtnForm');
        if (confirmBtn) {
            confirmBtn.disabled = false;
        }
    },

    updateDiscountUnitForm: function() {
        const discountType = document.querySelector('input[name="discountTypeForm"]:checked').value;
        const unit = document.getElementById('discountUnitForm');
        unit.textContent = discountType === 'percentage' ? '%' : '€';
    },

    calculateDiscountForm: function() {
        // Calculer le sous-total en utilisant les prix actuels (possiblement modifiés)
        // Si des remises individuelles sont appliquées, utiliser les prix avant remise
        let subtotal = 0;
        this.currentOrder.forEach(item => {
            if (item.hasIndividualDiscount && item.priceBeforeDiscount) {
                subtotal += item.quantity * item.priceBeforeDiscount;
            } else {
                subtotal += item.total_price;
            }
        });

        const discountValue = parseFloat(document.getElementById('discountValueForm').value) || 0;
        const discountType = document.querySelector('input[name="discountTypeForm"]:checked').value;

        let discountAmount = 0;
        if (discountValue > 0) {
            if (discountType === 'percentage') {
                discountAmount = subtotal * (discountValue / 100);
            } else {
                discountAmount = Math.min(discountValue, subtotal);
            }
        }

        const newTotal = subtotal - discountAmount;

        const calculatedDiscountElement = document.getElementById('calculatedDiscountForm');
        const newTotalElement = document.getElementById('newTotalForm');

        if (calculatedDiscountElement) calculatedDiscountElement.textContent = discountAmount.toFixed(2) + ' €';
        if (newTotalElement) newTotalElement.textContent = newTotal.toFixed(2) + ' €';
    },

    applyDiscountForm: function() {
        const discountValue = parseFloat(document.getElementById('discountValueForm').value) || 0;
        const discountType = document.querySelector('input[name="discountTypeForm"]:checked').value;
        const applyToItems = document.getElementById('applyToItemsForm').checked;

        if (discountValue <= 0) {
            alert('Veuillez entrer une valeur de remise valide');
            return;
        }

        // Réinitialiser les remises individuelles précédentes
        this.resetIndividualDiscountsForm();

        this.discount = {
            amount: discountValue,
            type: discountType,
            applyToItems: applyToItems
        };

        // Si on applique aux articles individuels
        if (applyToItems) {
            this.applyDiscountToItemsForm();
        }

        // Mettre à jour l'affichage
        this.updateOrderDisplay();

        // Fermer la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('discountModalForm'));
        modal.hide();
    },

    resetIndividualDiscountsForm: function() {
        this.currentOrder.forEach(item => {
            if (item.hasIndividualDiscount) {
                // Restaurer le prix avant remise (qui peut être un prix modifié manuellement)
                if (item.priceBeforeDiscount) {
                    item.unit_price = item.priceBeforeDiscount;
                    item.total_price = item.quantity * item.unit_price;
                }

                // Nettoyer les marqueurs de remise individuelle
                delete item.hasIndividualDiscount;
                delete item.discountAmount;
                delete item.priceBeforeDiscount;
            }
        });
    },

    processPayment: function() {
        if (this.currentOrder.length === 0) {
            alert('Aucun article dans la commande');
            return;
        }

        if (!this.selectedPaymentMethod) {
            alert('Veuillez sélectionner une méthode de paiement');
            return;
        }

        const processingType = document.querySelector('input[name="processingTypeForm"]:checked').value;
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);

        // Calculer la remise
        let discountAmount = 0;
        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        const orderData = {
            supplier_id: this.selectedSupplier,
            cart_items: this.currentOrder.map(item => ({
                type: item.type,
                product_id: item.type === 'product' ? item.id : null,
                ingredient_id: item.type === 'ingredient' ? item.id : null,
                quantity: item.quantity,
                unit_price: item.unit_price,
                original_unit_price: item.originalPrice || item.unit_price,
                total_price: item.total_price,
                notes: item.notes || '',
                price_change_reason: item.priceChangeReason || null
            })),
            subtotal: subtotal,
            discount: {
                amount: this.discount.amount,
                type: this.discount.type,
                apply_to_items: this.discount.applyToItems
            },
            total_amount: total,
            processing_type: processingType,
            payment_method: this.selectedPaymentMethod,
            notes: ''
        };

        // Envoyer la commande
        fetch('/inventory/stock-replenishment/process-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande créée avec succès!');
                this.clearOrder();
                bootstrap.Modal.getInstance(document.getElementById('paymentModalForm')).hide();

                // Rediriger vers les détails de la commande
                if (data.purchase_order_id) {
                    window.location.href = `/inventory/stock-replenishment/orders/${data.purchase_order_id}`;
                }
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    },

    applyDiscountToItemsForm: function() {
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);
        let totalDiscountAmount = 0;

        if (this.discount.type === 'percentage') {
            totalDiscountAmount = subtotal * (this.discount.amount / 100);
        } else {
            totalDiscountAmount = Math.min(this.discount.amount, subtotal);
        }

        // Répartir la remise proportionnellement
        this.currentOrder.forEach(item => {
            // Sauvegarder le prix de référence initial si ce n'est pas déjà fait
            if (!item.referencePrice) {
                item.referencePrice = item.originalPrice || item.unit_price;
            }

            // Sauvegarder le prix avant application de remise (prix actuel, possiblement modifié)
            if (!item.priceBeforeDiscount) {
                item.priceBeforeDiscount = item.unit_price;
            }

            const itemProportion = item.total_price / subtotal;
            const itemDiscount = totalDiscountAmount * itemProportion;
            const itemDiscountPerUnit = itemDiscount / item.quantity;

            // Appliquer la remise sur le prix actuel (pas le prix de référence)
            item.unit_price = Math.max(0, item.priceBeforeDiscount - itemDiscountPerUnit);
            item.total_price = item.quantity * item.unit_price;

            // Marquer que cet article a une remise appliquée
            item.hasIndividualDiscount = true;
            item.discountAmount = itemDiscount;
        });

        // Garder les informations de remise pour l'envoi au serveur
        // Ne pas réinitialiser applyToItems car on en a besoin côté serveur
    },

    openPaymentModalForm: function() {
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);
        let discountAmount = 0;

        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        document.getElementById('paymentSubtotalForm').textContent = subtotal.toFixed(2) + ' €';

        if (discountAmount > 0) {
            document.getElementById('paymentDiscountRowForm').style.display = 'flex';
            document.getElementById('paymentDiscountForm').textContent = '-' + discountAmount.toFixed(2) + ' €';
        } else {
            document.getElementById('paymentDiscountRowForm').style.display = 'none';
        }

        document.getElementById('paymentTotalForm').textContent = total.toFixed(2) + ' €';

        const modal = new bootstrap.Modal(document.getElementById('paymentModalForm'));
        modal.show();
    },

    processPaymentForm: function() {
        const paymentMethod = document.querySelector('input[name="paymentMethodForm"]:checked').value;

        // Préparer les données de commande avec les nouvelles informations
        const subtotal = this.currentOrder.reduce((sum, item) => sum + item.total_price, 0);

        let discountAmount = 0;
        if (this.discount.amount > 0) {
            if (this.discount.type === 'percentage') {
                discountAmount = subtotal * (this.discount.amount / 100);
            } else {
                discountAmount = Math.min(this.discount.amount, subtotal);
            }
        }

        const total = subtotal - discountAmount;

        const orderData = {
            supplier_id: this.selectedSupplier,
            cart_items: this.currentOrder.map(item => ({
                type: item.type,
                product_id: item.type === 'product' ? item.id : null,
                ingredient_id: item.type === 'ingredient' ? item.id : null,
                quantity: item.quantity,
                unit_price: item.unit_price,
                original_unit_price: item.originalPrice || item.unit_price,
                total_price: item.total_price,
                price_change_reason: item.priceChangeReason || null
            })),
            subtotal: subtotal,
            discount: {
                amount: this.discount.amount,
                type: this.discount.type,
                apply_to_items: this.discount.applyToItems
            },
            total_amount: total,
            processing_type: 'receive_and_pay',
            payment_method: paymentMethod,
            notes: document.getElementById('generalNotes').value || ''
        };

        // Envoyer la commande
        this.submitOrderWithPayment(orderData);
    },

    submitOrderWithPayment: function(orderData) {
        fetch('/inventory/stock-replenishment/process-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Commande traitée avec succès!');
                // Réinitialiser le formulaire
                this.currentOrder = [];
                this.discount = { amount: 0, type: 'amount', applyToItems: false };
                this.updateOrderDisplay();

                // Fermer les modales
                const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModalForm'));
                if (paymentModal) paymentModal.hide();

                const finalizeModal = bootstrap.Modal.getInstance(document.getElementById('finalizeModal'));
                if (finalizeModal) finalizeModal.hide();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur lors du traitement de la commande'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du traitement de la commande');
        });
    }
};

// Fonctions globales
function resetForm() {
    StockReplenishmentForm.resetForm();
}

function clearOrder() {
    StockReplenishmentForm.clearOrder();
}

function finalizeOrder() {
    StockReplenishmentForm.finalizeOrder();
}

function submitOrder() {
    StockReplenishmentForm.submitOrder();
}

function loadLastOrder() {
    // Fonctionnalité à implémenter
    alert('Fonctionnalité en cours de développement');
}

function loadTemplate() {
    // Fonctionnalité à implémenter
    alert('Fonctionnalité en cours de développement');
}

function showLowStockItems() {
    // Fonctionnalité à implémenter
    alert('Fonctionnalité en cours de développement');
}

function openDiscountModalForm() {
    StockReplenishmentForm.openDiscountModal();
}

function openPaymentModalForm() {
    StockReplenishmentForm.openPaymentModal();
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    StockReplenishmentForm.init();

    // Event listeners pour les nouvelles fonctionnalités
    document.querySelectorAll('input[name="discountTypeForm"]').forEach(radio => {
        radio.addEventListener('change', () => {
            StockReplenishmentForm.updateDiscountUnitForm();
            StockReplenishmentForm.calculateDiscountForm();
        });
    });

    document.getElementById('discountValueForm').addEventListener('input', () => {
        StockReplenishmentForm.calculateDiscountForm();
    });

    document.getElementById('confirmDiscountForm').addEventListener('click', () => {
        StockReplenishmentForm.applyDiscountForm();
    });

    document.getElementById('confirmPriceChangeForm').addEventListener('click', () => {
        StockReplenishmentForm.confirmPriceChangeForm();
    });

    const confirmPaymentBtn = document.getElementById('confirmPaymentBtnForm');
    if (confirmPaymentBtn) {
        confirmPaymentBtn.addEventListener('click', () => {
            StockReplenishmentForm.processPayment();
        });
    }

    // Event listeners pour les boutons de méthode de paiement
    document.querySelectorAll('.payment-method-btn-form').forEach(btn => {
        btn.addEventListener('click', () => {
            const method = btn.getAttribute('data-method');
            StockReplenishmentForm.selectPaymentMethod(method);
        });
    });
});
